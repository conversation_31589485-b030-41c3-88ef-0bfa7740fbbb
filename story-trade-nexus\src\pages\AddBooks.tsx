
import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { BookPlus, X, Image as ImageIcon, Check, MapPin, AlertCircle } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button-variants";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useAuth } from "@/lib/AuthContext";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { uploadMultipleImages } from "@/lib/storageService";
import { getCurrentPosition, GeoCoordinates } from "@/lib/geolocationUtils";

const bookSchema = z.object({
  title: z.string().min(1, { message: "Book title is required" }),
  author: z.string().min(1, { message: "Author name is required" }),
  isbn: z.string().optional(),
  genre: z.string().min(1, { message: "Please select at least one genre" }),
  condition: z.string().min(1, { message: "Please select a condition" }),
  description: z.string().min(10, { message: "Description should be at least 10 characters" }),
  availability: z.string().min(1, { message: "Please select availability option" }),
  price: z.string().optional(),
  rentalPrice: z.string().optional(),
  rentalPeriod: z.string().optional(),
  securityDepositRequired: z.boolean().optional().default(false),
  securityDepositAmount: z.string().optional(),
});

type BookFormValues = z.infer<typeof bookSchema>;

const AddBooks = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [displayImageIndex, setDisplayImageIndex] = useState<number>(0);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isCapturingLocation, setIsCapturingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [capturedLocation, setCapturedLocation] = useState<GeoCoordinates | null>(null);
  const { currentUser, userData } = useAuth();

  // Handle adding images
  const handleAddImages = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    // Check if adding these files would exceed the maximum
    if (imageFiles.length + files.length > 4) {
      toast.error("Maximum 4 images allowed");
      return;
    }

    // Convert FileList to array and add to existing files
    const newFiles = Array.from(files);
    const updatedFiles = [...imageFiles, ...newFiles];
    setImageFiles(updatedFiles);

    // Create preview URLs for the new files
    const newUrls = newFiles.map(file => URL.createObjectURL(file));
    const updatedUrls = [...imageUrls, ...newUrls];
    setImageUrls(updatedUrls);

    // If this is the first image, set it as the display image
    if (imageFiles.length === 0 && newFiles.length > 0) {
      setDisplayImageIndex(0);
    }
  };

  // Handle removing an image
  const handleRemoveImage = (index: number) => {
    // Create new arrays without the removed image
    const updatedFiles = [...imageFiles];
    const updatedUrls = [...imageUrls];

    // Remove the file and URL at the specified index
    updatedFiles.splice(index, 1);
    updatedUrls.splice(index, 1);

    // Update state
    setImageFiles(updatedFiles);
    setImageUrls(updatedUrls);

    // If we removed the display image, update the display image index
    if (index === displayImageIndex) {
      // If there are still images, set the first one as the display image
      if (updatedFiles.length > 0) {
        setDisplayImageIndex(0);
      } else {
        setDisplayImageIndex(0);
      }
    } else if (index < displayImageIndex) {
      // If we removed an image before the display image, adjust the index
      setDisplayImageIndex(displayImageIndex - 1);
    }
  };

  // Handle setting the display image
  const handleSetDisplayImage = (index: number) => {
    setDisplayImageIndex(index);
  };

  // Function to capture GPS location
  const captureGPSLocation = async (): Promise<GeoCoordinates | null> => {
    setIsCapturingLocation(true);
    setLocationError(null);

    try {
      console.log("Attempting to capture GPS location...");
      const coordinates = await getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 60000
      });

      console.log("GPS location captured successfully:", coordinates);
      setCapturedLocation(coordinates);
      return coordinates;
    } catch (error) {
      console.error("Error capturing GPS location:", error);

      let errorMessage = "Unable to get your current location.";
      if (error instanceof Error) {
        if (error.message.includes("permission")) {
          errorMessage = "Location permission denied. Using your registered address instead.";
        } else if (error.message.includes("timeout")) {
          errorMessage = "Location request timed out. Using your registered address instead.";
        } else if (error.message.includes("unavailable")) {
          errorMessage = "Location service unavailable. Using your registered address instead.";
        }
      }

      setLocationError(errorMessage);
      return null;
    } finally {
      setIsCapturingLocation(false);
    }
  };

  // Function to get fallback location from user's pincode
  const getFallbackLocation = async (): Promise<GeoCoordinates | null> => {
    if (!userData?.pincode) {
      console.log("No pincode available for fallback location");
      return null;
    }

    try {
      // Use a simple mapping for common Indian cities based on pincode
      // This is a basic fallback - in production, you'd use a comprehensive pincode database
      const pincodeToCoordinates: { [key: string]: GeoCoordinates } = {
        // Hyderabad area
        '500001': { latitude: 17.3850, longitude: 78.4867 },
        '500032': { latitude: 17.4399, longitude: 78.3489 },
        '500081': { latitude: 17.4485, longitude: 78.3908 },
        // Mumbai area
        '400001': { latitude: 18.9322, longitude: 72.8264 },
        '400051': { latitude: 19.0596, longitude: 72.8295 },
        // Delhi area
        '110001': { latitude: 28.6139, longitude: 77.2090 },
        '110016': { latitude: 28.5494, longitude: 77.2001 },
        // Bangalore area
        '560001': { latitude: 12.9716, longitude: 77.5946 },
        '560066': { latitude: 12.9698, longitude: 77.7500 },
        // Chennai area
        '600001': { latitude: 13.0827, longitude: 80.2707 },
        '600028': { latitude: 13.0569, longitude: 80.2091 },
      };

      const coordinates = pincodeToCoordinates[userData.pincode];
      if (coordinates) {
        console.log("Fallback location from pincode mapping:", coordinates);
        return coordinates;
      }

      // If pincode not in our mapping, generate approximate coordinates based on state
      const firstTwoDigits = userData.pincode.substring(0, 2);
      const stateCoordinates: { [key: string]: GeoCoordinates } = {
        '50': { latitude: 17.3850, longitude: 78.4867 }, // Telangana/Andhra Pradesh
        '40': { latitude: 19.0760, longitude: 72.8777 }, // Maharashtra
        '11': { latitude: 28.7041, longitude: 77.1025 }, // Delhi
        '56': { latitude: 12.9716, longitude: 77.5946 }, // Karnataka
        '60': { latitude: 13.0827, longitude: 80.2707 }, // Tamil Nadu
        '70': { latitude: 22.5726, longitude: 88.3639 }, // West Bengal
        '30': { latitude: 26.9124, longitude: 75.7873 }, // Rajasthan
        '22': { latitude: 26.8467, longitude: 80.9462 }, // Uttar Pradesh
      };

      const stateCoords = stateCoordinates[firstTwoDigits];
      if (stateCoords) {
        console.log("Fallback location from state mapping:", stateCoords);
        return stateCoords;
      }

      console.log("No fallback location available for pincode:", userData.pincode);
      return null;
    } catch (error) {
      console.error("Error getting fallback location from pincode:", error);
      return null;
    }
  };

  // Function to retrieve user's community from Firestore
  const getUserCommunity = async (): Promise<string | null> => {
    if (!currentUser?.uid) {
      console.log("No current user available for community retrieval");
      return null;
    }

    try {
      console.log("Retrieving user community from Firestore...");

      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firestore functions
      const { doc, getDoc, getFirestore } = await import('firebase/firestore');

      const db = getFirestore();
      const userRef = doc(db, 'users', currentUser.uid);
      const userSnapshot = await getDoc(userRef);

      if (userSnapshot.exists()) {
        const userData = userSnapshot.data();
        const community = userData.community;

        if (community && typeof community === 'string' && community.trim() !== '') {
          console.log("User community retrieved successfully:", community);
          return community.trim();
        } else {
          console.log("User community is empty or not set");
          return null;
        }
      } else {
        console.log("User document not found in Firestore");
        return null;
      }
    } catch (error) {
      console.error("Error retrieving user community:", error);
      return null;
    }
  };

  const form = useForm<BookFormValues>({
    resolver: zodResolver(bookSchema),
    defaultValues: {
      title: "",
      author: "",
      isbn: "",
      genre: "",
      condition: "",
      description: "",
      availability: "",
      price: "",
      rentalPrice: "",
      rentalPeriod: "per week",
      securityDepositRequired: false,
      securityDepositAmount: "",
    },
  });

  const onSubmit = async (values: BookFormValues) => {
    setIsLoading(true);
    try {
      console.log("Adding book:", values);

      // Import the createBook function
      const { createBook } = await import('@/lib/bookService');

      if (!currentUser) {
        toast.error("You must be signed in to add a book");
        setIsLoading(false);
        return;
      }

      // Capture GPS location first
      console.log("Capturing location for book listing...");
      let bookLocation: GeoCoordinates | null = null;

      // Try to get GPS location first
      bookLocation = await captureGPSLocation();

      // If GPS fails, try fallback to user's pincode location
      if (!bookLocation) {
        console.log("GPS capture failed, trying fallback location...");
        bookLocation = await getFallbackLocation();
      }

      if (bookLocation) {
        console.log("Location captured for book:", bookLocation);
      } else {
        console.log("No location could be determined for book");
      }

      // Retrieve user's community information
      console.log("Retrieving user community for book listing...");
      let userCommunity: string | null = null;

      try {
        userCommunity = await getUserCommunity();
        if (userCommunity) {
          console.log("User community retrieved for book:", userCommunity);
        } else {
          console.log("No community information available for user");
        }
      } catch (communityError) {
        console.error("Error retrieving user community:", communityError);
        // Continue without community - book submission should not fail
      }

      // Validate genre format
      let genreArray: string[] = [];
      try {
        genreArray = values.genre.split(',').map(g => g.trim()).filter(g => g.length > 0);
        if (genreArray.length === 0) {
          genreArray = [values.genre.trim()];
        }
      } catch (genreError) {
        console.error("Error processing genre:", genreError);
        genreArray = [values.genre.trim()];
      }

      // Validate price and rental price
      let price: number | null = null;
      if (values.price) {
        price = Number(values.price);
        if (isNaN(price)) {
          toast.error("Invalid price value. Please enter a valid number.");
          setIsLoading(false);
          return;
        }
      }

      let rentalPrice: number | null = null;
      if (values.rentalPrice) {
        rentalPrice = Number(values.rentalPrice);
        if (isNaN(rentalPrice)) {
          toast.error("Invalid rental price value. Please enter a valid number.");
          setIsLoading(false);
          return;
        }
      }

      // Set rental period to null if rental price is not provided
      let rentalPeriod = values.rentalPeriod;
      if (!rentalPrice) {
        rentalPeriod = null;
      }

      // Handle security deposit
      let securityDepositAmount: number | null = null;
      if (values.securityDepositRequired && values.securityDepositAmount) {
        securityDepositAmount = Number(values.securityDepositAmount);
        if (isNaN(securityDepositAmount)) {
          toast.error("Invalid security deposit amount. Please enter a valid number.");
          setIsLoading(false);
          return;
        }
      }

      // Upload images if there are any
      let uploadedImageUrls: string[] = [];
      let mainImageUrl = 'https://via.placeholder.com/150?text=No+Image';

      if (imageFiles.length > 0) {
        try {
          toast.info("Uploading images...");

          // Upload all images
          uploadedImageUrls = await uploadMultipleImages(
            imageFiles,
            currentUser.uid,
            (progress) => {
              setUploadProgress(progress);
              console.log(`Upload progress: ${progress}%`);
            }
          );

          console.log("Uploaded image URLs:", uploadedImageUrls);

          // Set the main image URL to the display image
          if (uploadedImageUrls.length > 0) {
            // Use the selected display image index, or default to the first image
            mainImageUrl = uploadedImageUrls[displayImageIndex] || uploadedImageUrls[0];
          }
        } catch (uploadError) {
          console.error("Error uploading images:", uploadError);
          toast.error("Failed to upload images. Using default image instead.");
          // Continue with the default image
        }
      }

      // Prepare book data
      const bookData = {
        title: values.title.trim(),
        author: values.author.trim(),
        isbn: values.isbn?.trim() || null,
        genre: genreArray,
        condition: values.condition,
        description: values.description.trim(),
        imageUrl: mainImageUrl,
        imageUrls: uploadedImageUrls.length > 0 ? uploadedImageUrls : undefined,
        displayImageIndex: uploadedImageUrls.length > 0 ? displayImageIndex : undefined,
        availability: values.availability,
        price: price,
        rentalPrice: rentalPrice,
        rentalPeriod: rentalPeriod,
        securityDepositRequired: values.securityDepositRequired,
        securityDepositAmount: securityDepositAmount,
        ownerId: currentUser.uid,
        ownerName: currentUser.displayName || currentUser.email?.split('@')[0] || 'Unknown',
        ownerEmail: currentUser.email || undefined,
        ownerCommunity: userCommunity || undefined, // Primary location identifier
        ownerCoordinates: bookLocation, // GPS coordinates captured during submission
        ownerPincode: userData?.pincode || undefined,
        ownerRating: 0, // Default rating for new users
        perceivedValue: 5, // Default value since we removed the field
      };

      console.log("Prepared book data:", bookData);

      // Create the book in Firestore
      const bookId = await createBook(bookData);

      console.log("Book created successfully with ID:", bookId);
      toast.success("Book added successfully! It will be visible after admin approval.");
      navigate("/browse");
    } catch (error) {
      console.error("Error adding book:", error);

      // Provide more detailed error message
      let errorMessage = "Failed to add book. Please try again.";

      if (error instanceof Error) {
        // If it's a standard Error object, get the message
        errorMessage = `Error: ${error.message}`;
        console.error("Error details:", error.message);

        // Check for specific Firebase error codes
        if (error.message.includes("permission-denied")) {
          errorMessage = "You don't have permission to add books. Please check your account.";
        } else if (error.message.includes("network")) {
          errorMessage = "Network error. Please check your internet connection and try again.";
        } else if (error.message.includes("quota-exceeded")) {
          errorMessage = "Database quota exceeded. Please try again later.";
        }
      }

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
      setUploadProgress(0);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        <div className="container mx-auto px-4 py-8 max-w-2xl">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-navy-800 font-playfair mb-2">Add Your Book</h1>
              <p className="text-gray-600">Share your book with the community</p>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-6">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Book Title*</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter book title" disabled={isLoading} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="author"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Author*</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter author name" disabled={isLoading} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="isbn"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ISBN (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter ISBN" disabled={isLoading} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="genre"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Genre*</FormLabel>
                          <FormControl>
                            <select
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              disabled={isLoading}
                              {...field}
                            >
                              <option value="">Select Genre</option>
                              <option value="Fiction">Fiction</option>
                              <option value="Non-Fiction">Non-Fiction</option>
                              <option value="Classics">Classics</option>
                              <option value="Fantasy">Fantasy</option>
                              <option value="Mystery">Mystery</option>
                              <option value="Romance">Romance</option>
                              <option value="Science Fiction">Science Fiction</option>
                              <option value="History">History</option>
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="condition"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Condition*</FormLabel>
                          <FormControl>
                            <select
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              disabled={isLoading}
                              {...field}
                            >
                              <option value="">Select Condition</option>
                              <option value="New">New</option>
                              <option value="Like New">Like New</option>
                              <option value="Good">Good</option>
                              <option value="Fair">Fair</option>
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="space-y-6">
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                      <div className="flex flex-col items-center justify-center mb-4">
                        <h3 className="text-lg font-medium text-gray-700 mb-2">Book Images</h3>
                        <p className="text-sm text-gray-500 mb-2 text-center">
                          Upload up to 4 images of your book. The first image will be the display image.
                        </p>
                        <div className="flex items-center justify-center mb-2">
                          <span className="text-sm font-medium text-gray-700 mr-2">
                            {imageFiles.length}/4 images
                          </span>
                          {uploadProgress > 0 && uploadProgress < 100 && (
                            <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-burgundy-500"
                                style={{ width: `${uploadProgress}%` }}
                              ></div>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Display uploaded images */}
                      {imageUrls.length > 0 && (
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          {imageUrls.map((url, index) => (
                            <div
                              key={index}
                              className={`relative border rounded-md overflow-hidden ${
                                index === displayImageIndex ? 'ring-2 ring-burgundy-500' : ''
                              }`}
                            >
                              <img
                                src={url}
                                alt={`Book image ${index + 1}`}
                                className="w-full h-32 object-contain"
                              />
                              <div className="absolute top-0 right-0 p-1 flex space-x-1">
                                {index !== displayImageIndex && (
                                  <button
                                    type="button"
                                    className="bg-burgundy-500 text-white p-1 rounded-full hover:bg-burgundy-600"
                                    onClick={() => handleSetDisplayImage(index)}
                                    title="Set as display image"
                                  >
                                    <Check className="h-4 w-4" />
                                  </button>
                                )}
                                <button
                                  type="button"
                                  className="bg-gray-700 text-white p-1 rounded-full hover:bg-gray-800"
                                  onClick={() => handleRemoveImage(index)}
                                  title="Remove image"
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              </div>
                              {index === displayImageIndex && (
                                <div className="absolute bottom-0 left-0 right-0 bg-burgundy-500 text-white text-xs py-1 text-center">
                                  Display Image
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Upload button */}
                      {imageFiles.length < 4 && (
                        <div className="flex justify-center">
                          <input
                            type="file"
                            id="image-upload"
                            className="hidden"
                            accept="image/*"
                            multiple={imageFiles.length < 3} // Allow multiple only if we can add more than 1
                            onChange={handleAddImages}
                            disabled={isLoading}
                          />
                          <label
                            htmlFor="image-upload"
                            className={`flex items-center justify-center text-sm bg-burgundy-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-burgundy-600 ${
                              isLoading ? 'opacity-50 cursor-not-allowed' : ''
                            }`}
                          >
                            <ImageIcon className="h-4 w-4 mr-2" />
                            {imageFiles.length === 0 ? 'Upload Images' : 'Add More Images'}
                          </label>
                        </div>
                      )}
                    </div>

                    <FormField
                      control={form.control}
                      name="availability"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Availability*</FormLabel>
                          <FormControl>
                            <select
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              disabled={isLoading}
                              onChange={(e) => {
                                field.onChange(e);
                              }}
                              value={field.value}
                            >
                              <option value="">Select Availability</option>
                              <option value="For Exchange">For Exchange</option>
                              <option value="For Sale">For Sale</option>
                              <option value="For Rent">For Rent</option>
                              <option value="For Sale & Exchange">For Sale & Exchange</option>
                              <option value="For Rent & Exchange">For Rent & Exchange</option>
                              <option value="For Rent & Sale">For Rent & Sale</option>
                              <option value="For Rent, Sale & Exchange">For Rent, Sale & Exchange</option>
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch("availability") &&
                     (form.watch("availability").includes("Sale") ||
                      form.watch("availability").includes("Rent")) && (
                      <div className="space-y-6">
                        {form.watch("availability").includes("Sale") && (
                          <FormField
                            control={form.control}
                            name="price"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Sale Price (₹)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="Enter price"
                                    disabled={isLoading}
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )}

                        {form.watch("availability").includes("Rent") && (
                          <>
                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name="rentalPrice"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Rental Price (₹)</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        placeholder="Enter rental price"
                                        disabled={isLoading}
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="rentalPeriod"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Period</FormLabel>
                                    <FormControl>
                                      <select
                                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        disabled={isLoading}
                                        {...field}
                                      >
                                        <option value="per day">Per Day</option>
                                        <option value="per week">Per Week</option>
                                        <option value="per month">Per Month</option>
                                      </select>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            <div className="mt-4">
                              <FormField
                                control={form.control}
                                name="securityDepositRequired"
                                render={({ field }) => (
                                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                                    <FormControl>
                                      <input
                                        type="checkbox"
                                        className="h-4 w-4 mt-1"
                                        checked={field.value}
                                        onChange={(e) => field.onChange(e.target.checked)}
                                        disabled={isLoading}
                                      />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                      <FormLabel>Security Deposit Required</FormLabel>
                                      <p className="text-sm text-gray-500">
                                        Require a security deposit for renting this book
                                      </p>
                                    </div>
                                  </FormItem>
                                )}
                              />
                            </div>

                            {form.watch("securityDepositRequired") && (
                              <div className="mt-4">
                                <FormField
                                  control={form.control}
                                  name="securityDepositAmount"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Security Deposit Amount (₹)</FormLabel>
                                      <FormControl>
                                        <Input
                                          type="number"
                                          placeholder="Enter security deposit amount"
                                          disabled={isLoading}
                                          {...field}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description*</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe the book, its condition, and any other details..."
                          className="min-h-[120px]"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Location Consent Disclaimer */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <div className="flex items-start space-x-3">
                    <MapPin className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h3 className="text-sm font-medium text-blue-900 mb-2">
                        Location Information
                      </h3>
                      <p className="text-sm text-blue-800 mb-2">
                        When you submit this book listing, we will automatically capture your current GPS location to help other users find books near them. This location data will be used solely for:
                      </p>
                      <ul className="text-sm text-blue-800 list-disc list-inside space-y-1 mb-3">
                        <li>Calculating and displaying distance to other users browsing books</li>
                        <li>Helping users find books in their local area</li>
                        <li>Improving the book discovery experience</li>
                      </ul>
                      <p className="text-sm text-blue-800">
                        If GPS location cannot be accessed, we'll use your registered address as a fallback. Your exact location will not be displayed to other users - only the calculated distance.
                      </p>

                      {/* Location Status Indicators */}
                      {isCapturingLocation && (
                        <div className="mt-3 flex items-center space-x-2 text-sm text-blue-700">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                          <span>Capturing your location...</span>
                        </div>
                      )}

                      {capturedLocation && (
                        <div className="mt-3 flex items-center space-x-2 text-sm text-green-700">
                          <Check className="h-4 w-4 text-green-600" />
                          <span>Location captured successfully</span>
                        </div>
                      )}

                      {locationError && (
                        <div className="mt-3 flex items-start space-x-2 text-sm text-amber-700">
                          <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                          <span>{locationError}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    disabled={isLoading}
                    onClick={() => navigate("/")}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="flex items-center gap-2"
                  >
                    <BookPlus className="h-4 w-4" />
                    Add Book
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default AddBooks;
